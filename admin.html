<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Quản lý người dùng</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg">
            <div class="p-4 border-b">
                <h2 class="text-xl font-semibold text-gray-800">Admin Panel</h2>
            </div>
            <nav class="mt-4">
                <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                    <i class="fas fa-chart-bar mr-3"></i>
                    Quản lý log
                </a>
                <a href="#" class="flex items-center px-4 py-3 text-blue-600 bg-blue-50 border-r-2 border-blue-600">
                    <i class="fas fa-users mr-3"></i>
                    Quản lý tài khoản
                </a>
                <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                    <i class="fas fa-user-friends mr-3"></i>
                    Quản lý tin nhắn
                </a>
                <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                    <i class="fas fa-users-cog mr-3"></i>
                    Quản lý khách hàng
                </a>
                <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                    <i class="fas fa-tachometer-alt mr-3"></i>
                    Dashboard
                </a>
                <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                    <i class="fas fa-chart-line mr-3"></i>
                    Báo cáo thống kê
                </a>
                <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                    <i class="fas fa-calendar mr-3"></i>
                    Cài đặt
                </a>
            </nav>
            <div class="absolute bottom-4 left-4">
                <a href="#" class="flex items-center text-gray-700 hover:text-blue-600">
                    <i class="fas fa-sign-out-alt mr-2"></i>
                    Đăng xuất
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2 text-sm text-gray-600">
                        <i class="fas fa-chevron-left cursor-pointer hover:text-gray-800"></i>
                        <span>Quản lý tài khoản</span>
                        <span>/</span>
                        <span>Tài khoản</span>
                        <span>/</span>
                        <span>18503942</span>
                        <span>/</span>
                        <span>Chỉnh sửa</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <i class="fas fa-question-circle text-gray-400 cursor-pointer"></i>
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="p-6">
                <div class="bg-white rounded-lg shadow-sm">
                    <!-- User Header -->
                    <div class="px-6 py-4 border-b">
                        <div class="flex items-center justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-gray-800">
                                    <span class="text-blue-600">18503942</span> 
                                    <span class="text-red-500">NGUYEN VAN DUY</span>
                                </h1>
                                <div class="flex items-center mt-2">
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">Hiệu lực</span>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <span class="text-sm text-gray-600 mr-2">Yêu cầu người dùng khi đăng nhập</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer" checked>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Form Content -->
                    <div class="p-6">
                        <div class="mb-6">
                            <div class="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center justify-between">
                                <span class="text-red-700">Chọn hành động của người dùng khi đăng nhập</span>
                                <i class="fas fa-times text-red-400 cursor-pointer"></i>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Tên đăng nhập*
                                </label>
                                <input type="text" value="18503942" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Họ và tên*
                                </label>
                                <input type="text" value="NGUYEN VAN DUY" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Email*
                                </label>
                                <input type="email" value="<EMAIL>" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Đơn vị quản lý*
                                </label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option>HÀ ĐÔNG T/O</option>
                                </select>
                            </div>
                        </div>

                        <!-- User Info Grid -->
                        <div class="grid grid-cols-4 gap-6 mt-8">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Người tạo</label>
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fas fa-user mr-2"></i>
                                    NGUYEN NGOC BAO TRAM
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Ngày tạo</label>
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fas fa-calendar mr-2"></i>
                                    15/08/2024 15:39:00
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Người cập nhật gần nhất</label>
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fas fa-user mr-2"></i>
                                    NGUYEN NGOC BAO TRAM
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Ngày cập nhật gần nhất</label>
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fas fa-calendar mr-2"></i>
                                    15/08/2024 15:39:00
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex justify-center space-x-4 mt-8">
                            <button class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-md flex items-center">
                                <i class="fas fa-save mr-2"></i>
                                Lưu
                            </button>
                            <button class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-md flex items-center">
                                <i class="fas fa-times mr-2"></i>
                                Hủy
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
