<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Quản lý người dùng</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .scrollbar-thin {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e1 #f1f5f9;
        }
        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }
        .scrollbar-thin::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        .smooth-transition {
            transition: all 0.2s ease-in-out;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="w-64 bg-white border-r border-gray-200 flex flex-col">
            <!-- Sidebar Header -->
            <div class="p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-cyan-50">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-shield-alt text-blue-600"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">Admin Panel</h2>
                        <p class="text-sm text-blue-600">Quản trị hệ thống</p>
                    </div>
                </div>
            </div>

            <!-- Sidebar Navigation -->
            <nav class="flex-1 overflow-y-auto scrollbar-thin p-4 space-y-1">
                <a href="#" class="flex items-center px-3 py-2.5 text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-700 smooth-transition">
                    <i class="fas fa-chart-bar w-5 text-center mr-3 text-gray-400"></i>
                    <span class="font-medium">Quản lý log</span>
                </a>
                <a href="#" class="flex items-center px-3 py-2.5 text-white bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg shadow-sm">
                    <i class="fas fa-users w-5 text-center mr-3 text-white"></i>
                    <span class="font-medium">Quản lý tài khoản</span>
                </a>
                <a href="#" class="flex items-center px-3 py-2.5 text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-700 smooth-transition">
                    <i class="fas fa-comments w-5 text-center mr-3 text-gray-400"></i>
                    <span class="font-medium">Quản lý tin nhắn</span>
                </a>
                <a href="#" class="flex items-center px-3 py-2.5 text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-700 smooth-transition">
                    <i class="fas fa-users-cog w-5 text-center mr-3 text-gray-400"></i>
                    <span class="font-medium">Quản lý khách hàng</span>
                </a>
                <a href="#" class="flex items-center px-3 py-2.5 text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-700 smooth-transition">
                    <i class="fas fa-tachometer-alt w-5 text-center mr-3 text-gray-400"></i>
                    <span class="font-medium">Dashboard</span>
                </a>
                <a href="#" class="flex items-center px-3 py-2.5 text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-700 smooth-transition">
                    <i class="fas fa-chart-line w-5 text-center mr-3 text-gray-400"></i>
                    <span class="font-medium">Báo cáo thống kê</span>
                </a>
                <a href="#" class="flex items-center px-3 py-2.5 text-gray-600 rounded-lg hover:bg-blue-50 hover:text-blue-700 smooth-transition">
                    <i class="fas fa-cog w-5 text-center mr-3 text-gray-400"></i>
                    <span class="font-medium">Cài đặt</span>
                </a>
            </nav>

            <!-- Sidebar Footer -->
            <div class="p-4 border-t border-gray-200">
                <a href="#" class="flex items-center justify-center w-full px-3 py-2.5 text-gray-600 bg-gray-50 rounded-lg hover:bg-gray-100 hover:text-gray-900 smooth-transition">
                    <i class="fas fa-sign-out-alt mr-2"></i>
                    <span class="font-medium">Đăng xuất</span>
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-white border-b border-gray-200 px-6 py-4 flex-shrink-0">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button class="w-9 h-9 bg-blue-100 rounded-lg flex items-center justify-center text-blue-600 hover:bg-blue-200 smooth-transition">
                            <i class="fas fa-chevron-left text-sm"></i>
                        </button>
                        <nav class="flex items-center space-x-2 text-sm">
                            <span class="text-gray-500">Quản lý tài khoản</span>
                            <i class="fas fa-chevron-right text-gray-300 text-xs"></i>
                            <span class="text-gray-500">Tài khoản</span>
                            <i class="fas fa-chevron-right text-gray-300 text-xs"></i>
                            <span class="text-blue-600 font-medium">18503942</span>
                            <i class="fas fa-chevron-right text-gray-300 text-xs"></i>
                            <span class="text-gray-900 font-medium">Chỉnh sửa</span>
                        </nav>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="w-9 h-9 bg-blue-50 rounded-lg flex items-center justify-center text-blue-500 hover:bg-blue-100 smooth-transition">
                            <i class="fas fa-question-circle text-sm"></i>
                        </button>
                        <div class="flex items-center space-x-3">
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-900">Admin User</p>
                                <p class="text-xs text-blue-600">Quản trị viên</p>
                            </div>
                            <div class="w-9 h-9 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="flex-1 overflow-y-auto scrollbar-thin p-6">
                <div class="max-w-5xl mx-auto">
                    <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
                        <!-- User Header -->
                        <div class="px-6 py-5 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-cyan-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-user text-blue-600 text-lg"></i>
                                    </div>
                                    <div>
                                        <h1 class="text-2xl font-bold text-gray-900">
                                            <span class="text-blue-600">#18503942</span>
                                        </h1>
                                        <h2 class="text-lg font-semibold text-gray-700 mt-1">NGUYEN VAN DUY</h2>
                                        <div class="flex items-center mt-2">
                                            <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                                                <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                                Hiệu lực
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center bg-white rounded-lg border border-blue-200 p-4 shadow-sm">
                                    <span class="text-gray-700 font-medium mr-4">Yêu cầu người dùng khi đăng nhập</span>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer" checked>
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Form Content -->
                        <div class="p-6">
                            <!-- Alert -->
                            <div class="mb-6">
                                <div class="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i class="fas fa-exclamation-triangle text-red-500 mr-3"></i>
                                        <span class="text-red-700 font-medium">Chọn hành động của người dùng khi đăng nhập</span>
                                    </div>
                                    <button class="text-red-400 hover:text-red-600 smooth-transition">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Form Fields -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-user text-blue-500 mr-2"></i>
                                        Tên đăng nhập*
                                    </label>
                                    <input type="text" value="18503942"
                                           class="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 smooth-transition">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-id-card text-blue-500 mr-2"></i>
                                        Họ và tên*
                                    </label>
                                    <input type="text" value="NGUYEN VAN DUY"
                                           class="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 smooth-transition">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-envelope text-blue-500 mr-2"></i>
                                        Email*
                                    </label>
                                    <input type="email" value="<EMAIL>"
                                           class="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 smooth-transition">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-building text-blue-500 mr-2"></i>
                                        Đơn vị quản lý*
                                    </label>
                                    <select class="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 smooth-transition">
                                        <option>HÀ ĐÔNG T/O</option>
                                        <option>HÀ NỘI T/O</option>
                                        <option>HỒ CHÍ MINH T/O</option>
                                    </select>
                                </div>
                            </div>

                            <!-- System Info -->
                            <div class="mt-8 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-6 border border-blue-100">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                    Thông tin hệ thống
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="bg-white rounded-lg p-4 border border-blue-100 shadow-sm">
                                        <div class="flex items-center mb-3">
                                            <i class="fas fa-user-plus text-blue-500 mr-2"></i>
                                            <label class="text-sm font-medium text-gray-700">Người tạo</label>
                                        </div>
                                        <div class="text-gray-900 font-medium">NGUYEN NGOC BAO TRAM</div>
                                        <div class="text-sm text-blue-600 mt-1">15/08/2024 15:39:00</div>
                                    </div>
                                    <div class="bg-white rounded-lg p-4 border border-blue-100 shadow-sm">
                                        <div class="flex items-center mb-3">
                                            <i class="fas fa-user-edit text-blue-500 mr-2"></i>
                                            <label class="text-sm font-medium text-gray-700">Cập nhật gần nhất</label>
                                        </div>
                                        <div class="text-gray-900 font-medium">NGUYEN NGOC BAO TRAM</div>
                                        <div class="text-sm text-blue-600 mt-1">15/08/2024 15:39:00</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex flex-wrap justify-center gap-3 mt-8 pt-6 border-t border-gray-200">
                                <button class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2.5 rounded-lg flex items-center smooth-transition font-medium shadow-sm">
                                    <i class="fas fa-save mr-2"></i>
                                    Lưu thay đổi
                                </button>
                                <button class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2.5 rounded-lg flex items-center smooth-transition font-medium">
                                    <i class="fas fa-times mr-2"></i>
                                    Hủy bỏ
                                </button>
                                <button class="bg-red-600 hover:bg-red-700 text-white px-6 py-2.5 rounded-lg flex items-center smooth-transition font-medium">
                                    <i class="fas fa-trash mr-2"></i>
                                    Xóa tài khoản
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
