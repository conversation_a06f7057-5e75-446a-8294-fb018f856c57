<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Quản lý người dùng</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-shadow {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .hover-scale {
            transition: transform 0.2s ease-in-out;
        }
        .hover-scale:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 min-h-screen">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-2xl relative">
            <div class="gradient-bg p-6 text-white">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                        <i class="fas fa-crown text-yellow-300"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-bold">Admin Panel</h2>
                        <p class="text-sm opacity-80">Quản trị hệ thống</p>
                    </div>
                </div>
            </div>
            <nav class="mt-2 px-2">
                <a href="#" class="flex items-center px-4 py-3 mx-2 my-1 text-gray-600 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-600 transition-all duration-200 hover-scale">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-chart-bar text-orange-500"></i>
                    </div>
                    <span class="font-medium">Quản lý log</span>
                </a>
                <a href="#" class="flex items-center px-4 py-3 mx-2 my-1 text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg shadow-lg">
                    <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-users text-white"></i>
                    </div>
                    <span class="font-medium">Quản lý tài khoản</span>
                </a>
                <a href="#" class="flex items-center px-4 py-3 mx-2 my-1 text-gray-600 rounded-lg hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 hover:text-green-600 transition-all duration-200 hover-scale">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-comments text-green-500"></i>
                    </div>
                    <span class="font-medium">Quản lý tin nhắn</span>
                </a>
                <a href="#" class="flex items-center px-4 py-3 mx-2 my-1 text-gray-600 rounded-lg hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 hover:text-purple-600 transition-all duration-200 hover-scale">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-users-cog text-purple-500"></i>
                    </div>
                    <span class="font-medium">Quản lý khách hàng</span>
                </a>
                <a href="#" class="flex items-center px-4 py-3 mx-2 my-1 text-gray-600 rounded-lg hover:bg-gradient-to-r hover:from-cyan-50 hover:to-blue-50 hover:text-cyan-600 transition-all duration-200 hover-scale">
                    <div class="w-8 h-8 bg-cyan-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-tachometer-alt text-cyan-500"></i>
                    </div>
                    <span class="font-medium">Dashboard</span>
                </a>
                <a href="#" class="flex items-center px-4 py-3 mx-2 my-1 text-gray-600 rounded-lg hover:bg-gradient-to-r hover:from-yellow-50 hover:to-orange-50 hover:text-yellow-600 transition-all duration-200 hover-scale">
                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-chart-line text-yellow-500"></i>
                    </div>
                    <span class="font-medium">Báo cáo thống kê</span>
                </a>
                <a href="#" class="flex items-center px-4 py-3 mx-2 my-1 text-gray-600 rounded-lg hover:bg-gradient-to-r hover:from-gray-50 hover:to-slate-50 hover:text-gray-700 transition-all duration-200 hover-scale">
                    <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-cog text-gray-500"></i>
                    </div>
                    <span class="font-medium">Cài đặt</span>
                </a>
            </nav>
            <div class="absolute bottom-6 left-6 right-6">
                <a href="#" class="flex items-center justify-center w-full px-4 py-3 text-gray-600 bg-gray-50 rounded-lg hover:bg-red-50 hover:text-red-600 transition-all duration-200">
                    <i class="fas fa-sign-out-alt mr-2"></i>
                    <span class="font-medium">Đăng xuất</span>
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-lg border-b px-8 py-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <button class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center text-white hover:shadow-lg transition-all duration-200 hover-scale">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <nav class="flex items-center space-x-2 text-sm">
                            <span class="text-gray-500">Quản lý tài khoản</span>
                            <i class="fas fa-chevron-right text-gray-300 text-xs"></i>
                            <span class="text-gray-500">Tài khoản</span>
                            <i class="fas fa-chevron-right text-gray-300 text-xs"></i>
                            <span class="text-blue-600 font-medium">18503942</span>
                            <i class="fas fa-chevron-right text-gray-300 text-xs"></i>
                            <span class="text-gray-800 font-medium">Chỉnh sửa</span>
                        </nav>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center text-gray-500 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200">
                            <i class="fas fa-question-circle"></i>
                        </button>
                        <div class="flex items-center space-x-3">
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-800">Admin User</p>
                                <p class="text-xs text-gray-500">Quản trị viên</p>
                            </div>
                            <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="p-8">
                <div class="bg-white rounded-2xl shadow-2xl card-shadow">
                    <!-- User Header -->
                    <div class="px-8 py-6 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-t-2xl">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-user text-white text-2xl"></i>
                                </div>
                                <div>
                                    <h1 class="text-3xl font-bold text-white">
                                        <span class="text-yellow-300">#18503942</span>
                                    </h1>
                                    <h2 class="text-xl font-semibold text-white mt-1">NGUYEN VAN DUY</h2>
                                    <div class="flex items-center mt-3">
                                        <span class="bg-green-400 text-green-900 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                                            <i class="fas fa-check-circle mr-2"></i>
                                            Hiệu lực
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center bg-white bg-opacity-10 rounded-xl p-4">
                                <span class="text-white font-medium mr-4">Yêu cầu người dùng khi đăng nhập</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer" checked>
                                    <div class="w-14 h-7 bg-white bg-opacity-30 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-white peer-focus:ring-opacity-30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:bg-green-400"></div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Form Content -->
                    <div class="p-8">
                        <div class="mb-8">
                            <div class="bg-gradient-to-r from-red-50 to-pink-50 border-l-4 border-red-400 rounded-lg p-6 flex items-center justify-between shadow-sm">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                                        <i class="fas fa-exclamation-triangle text-red-500"></i>
                                    </div>
                                    <span class="text-red-700 font-medium">Chọn hành động của người dùng khi đăng nhập</span>
                                </div>
                                <button class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center text-red-400 hover:bg-red-200 transition-colors">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-8">
                            <div class="space-y-2">
                                <label class="block text-sm font-semibold text-gray-700 mb-3">
                                    <i class="fas fa-user mr-2 text-blue-500"></i>
                                    Tên đăng nhập*
                                </label>
                                <input type="text" value="18503942"
                                       class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 hover:bg-white">
                            </div>
                            <div class="space-y-2">
                                <label class="block text-sm font-semibold text-gray-700 mb-3">
                                    <i class="fas fa-id-card mr-2 text-green-500"></i>
                                    Họ và tên*
                                </label>
                                <input type="text" value="NGUYEN VAN DUY"
                                       class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 bg-gray-50 hover:bg-white">
                            </div>
                            <div class="space-y-2">
                                <label class="block text-sm font-semibold text-gray-700 mb-3">
                                    <i class="fas fa-envelope mr-2 text-purple-500"></i>
                                    Email*
                                </label>
                                <input type="email" value="<EMAIL>"
                                       class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 bg-gray-50 hover:bg-white">
                            </div>
                            <div class="space-y-2">
                                <label class="block text-sm font-semibold text-gray-700 mb-3">
                                    <i class="fas fa-building mr-2 text-orange-500"></i>
                                    Đơn vị quản lý*
                                </label>
                                <select class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 bg-gray-50 hover:bg-white">
                                    <option>HÀ ĐÔNG T/O</option>
                                    <option>HÀ NỘI T/O</option>
                                    <option>HỒ CHÍ MINH T/O</option>
                                </select>
                            </div>
                        </div>

                        <!-- User Info Grid -->
                        <div class="mt-12 bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-8">
                            <h3 class="text-lg font-bold text-gray-800 mb-6 flex items-center">
                                <i class="fas fa-info-circle text-blue-500 mr-3"></i>
                                Thông tin hệ thống
                            </h3>
                            <div class="grid grid-cols-2 gap-8">
                                <div class="bg-white rounded-xl p-6 shadow-sm hover-scale">
                                    <div class="flex items-center mb-4">
                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-user-plus text-blue-500"></i>
                                        </div>
                                        <label class="text-sm font-semibold text-gray-700">Người tạo</label>
                                    </div>
                                    <div class="text-gray-800 font-medium">NGUYEN NGOC BAO TRAM</div>
                                    <div class="text-sm text-gray-500 mt-1">15/08/2024 15:39:00</div>
                                </div>
                                <div class="bg-white rounded-xl p-6 shadow-sm hover-scale">
                                    <div class="flex items-center mb-4">
                                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-user-edit text-green-500"></i>
                                        </div>
                                        <label class="text-sm font-semibold text-gray-700">Cập nhật gần nhất</label>
                                    </div>
                                    <div class="text-gray-800 font-medium">NGUYEN NGOC BAO TRAM</div>
                                    <div class="text-sm text-gray-500 mt-1">15/08/2024 15:39:00</div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex justify-center space-x-6 mt-12">
                            <button class="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-8 py-4 rounded-xl flex items-center shadow-lg hover:shadow-xl transition-all duration-200 hover-scale font-medium">
                                <i class="fas fa-save mr-3"></i>
                                Lưu thay đổi
                            </button>
                            <button class="bg-gradient-to-r from-gray-400 to-gray-500 hover:from-gray-500 hover:to-gray-600 text-white px-8 py-4 rounded-xl flex items-center shadow-lg hover:shadow-xl transition-all duration-200 hover-scale font-medium">
                                <i class="fas fa-times mr-3"></i>
                                Hủy bỏ
                            </button>
                            <button class="bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white px-8 py-4 rounded-xl flex items-center shadow-lg hover:shadow-xl transition-all duration-200 hover-scale font-medium">
                                <i class="fas fa-trash mr-3"></i>
                                Xóa tài khoản
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
